using UnityEngine;
using KinematicCharacterController.FPS;

public class ViewModelHeadBob : MonoBehaviour
{
    [<PERSON><PERSON>("References")]
    [Tooltip("Reference to the parent HeadBob component")]
    public HeadBob parentHeadBob;

    [<PERSON><PERSON>("Bob Intensity")]
    [Tooltip("Multiplier for position bobbing (0-1 where 1 is full parent intensity)")]
    [Range(0f, 1f)]
    public float bobPositionMultiplier = 0.5f;

    [Tooltip("Multiplier for rotation bobbing (0-1 where 1 is full parent intensity)")]
    [Range(0f, 1f)]
    public float bobRotationMultiplier = 0.5f;

    [Tooltip("Multiplier for landing bounce effects (0-1 where 1 is full parent intensity)")]
    [Range(0f, 1f)]
    public float bounceMultiplier = 0.5f;

    [Header("Camera Motion")]
    [Tooltip("How quickly the viewmodel follows camera motion")]
    [Range(1f, 20f)]
    public float cameraFollowSpeed = 5f;

    [Tooltip("Maximum position offset for camera motion (how far the viewmodel can move)")]
    public Vector3 maxPositionOffset = new Vector3(0.1f, 0.1f, 0.05f);

    [Tooltip("Maximum rotation offset for camera motion (in degrees)")]
    public Vector3 maxRotationOffset = new Vector3(5f, 10f, 5f);

    [Tooltip("Deadzone for camera motion (minimum movement before viewmodel follows)")]
    [Range(0f, 1f)]
    public float cameraDeadzone = 0.05f;

    [Header("Side Sway")]
    [Tooltip("Enable side-sway effect when turning")]
    public bool enableSideSway = true;
    
    [Tooltip("How much the viewmodel moves horizontally when turning (higher = more movement)")]
    [Range(0f, 2f)]
    public float sideSwayAmount = 0.8f;
    
    [Tooltip("How quickly the viewmodel catches up with camera rotation (lower = more lag)")]
    [Range(0.1f, 10f)]
    public float sideSwayLerpSpeed = 3f;
    
    [Tooltip("Maximum side-sway distance in local space units")]
    public float maxSideSwayDistance = 0.1f;

    [Header("Grappling Hook Stabilization")]
    [Tooltip("Reduces viewmodel effects while grappling to prevent glitching")]
    [Range(0f, 1f)]
    public float grapplingEffectReduction = 0.2f;
    
    [Tooltip("How quickly the viewmodel stabilizes when grappling starts")]
    [Range(1f, 20f)]
    public float grapplingStabilizationSpeed = 10f;
    
    [Tooltip("Disable camera motion effects while grappling")]
    public bool disableCameraMotionWhileGrappling = true;
    
    [Tooltip("Disable side sway while grappling")]
    public bool disableSideSwayWhileGrappling = true;

    [Header("Terminal Velocity Settings")]
    [Tooltip("Maximum shake intensity during terminal velocity falls")]
    [Range(0f, 1f)]
    public float maxTerminalShakeMultiplier = 0.1f;

    [Tooltip("Speed at which terminal velocity shake is reduced")]
    public float terminalShakeDamping = 10f;
    
    [Header("Landing Effect Tuning")]
    [Tooltip("Vertical multiplier for landing bounce (stronger = bigger drop)")]
    [Range(0.5f, 2f)]
    public float landingVerticalMultiplier = 1.2f;
    
    [Tooltip("Horizontal multiplier for erratic landing effects")]
    [Range(0f, 1f)]
    public float erraticHorizontalMultiplier = 0.3f;

    [Header("Wall Collision Prevention")]
    [Tooltip("Should wall collision prevention be enabled")]
    public bool enableWallCollisionPrevention = true;
    
    [Tooltip("Enable debug visualization")]
    public bool showDebugVisuals = false;
    
    [Tooltip("How often to check for wall collisions (in seconds)")]
    public float wallCheckInterval = 0.02f;
    
    [Tooltip("Layers that are considered walls/obstacles")]
    public LayerMask wallLayers;
    
    [Tooltip("Distance to check for walls")]
    public float wallCheckDistance = 0.3f;
    
    [Tooltip("How quickly the viewmodel moves away from walls")]
    public float wallAvoidanceSpeed = 8f;
    
    [Tooltip("Maximum push-back distance when colliding with walls")]
    public float maxWallPushDistance = 0.15f;
    
    [Tooltip("Rotation applied when pushing away from walls (in degrees)")]
    public Vector3 wallCollisionRotation = new Vector3(5f, -10f, 5f);
    
    [Tooltip("Points to check for wall collisions (local to viewmodel)")]
    public Vector3[] collisionCheckPoints;

    [Header("Slide Visual Effects")]
    [Tooltip("Additional rotation applied to the viewmodel during slides (in degrees)")]
    public Vector3 slideViewModelRotation = new Vector3(0f, 0f, 45f);

    [Tooltip("Additional position offset during slides (local space)")]
    public Vector3 slideViewModelOffset = new Vector3(0.1f, -0.05f, 0f);

    [Tooltip("How quickly the slide effects are applied")]
    [Range(1f, 20f)]
    public float slideViewModelSpeed = 10f;

    // Private variables
    private Vector3 initialLocalPosition;
    private Quaternion initialLocalRotation;
    private Vector3 lastCameraEulerAngles;
    private Vector3 currentCameraVelocity;
    private Vector3 smoothedCameraVelocity;
    private float cameraSmoothingFactor = 0.8f;
    
    // Side-sway variables
    private float targetSideSwayOffset = 0f;
    private float currentSideSwayOffset = 0f;
    private float lastCameraYaw = 0f;
    private Quaternion lastCameraRotationQuat;
    private Vector3 sideSwayVelocity;
    
    // Additional state tracking
    private bool isFlying = false;
    private float effectiveShakeMultiplier = 0f;
    
    // Grappling state
    private bool isGrappling = false;
    private float grapplingTransitionBlend = 0f;

    // Random values for erratic bounces
    private Vector3 randomBounceDirection;
    
    // Wall collision variables
    private Vector3 wallAvoidanceOffset = Vector3.zero;
    private Quaternion wallAvoidanceRotation = Quaternion.identity;
    private bool isCollidingWithWall = false;

    // Cached transform references
    private Transform cachedTransform;
    private Transform cachedCameraTransform;
    
    // Raycast optimization
    private RaycastHit[] raycastHits;
    private float nextWallCheckTime;
    private const int MAX_RAYCAST_HITS = 8;

    private Quaternion currentSlideViewModelRotation = Quaternion.identity;
    private Vector3 currentSlideViewModelPosition = Vector3.zero;
    private bool wasSliding = false;

    private void Start()
    {
        if (parentHeadBob == null)
        {
            parentHeadBob = GetComponentInParent<HeadBob>();
            if (parentHeadBob == null)
            {
                parentHeadBob = FindObjectOfType<HeadBob>();
                if (parentHeadBob == null)
                {
                    Debug.LogError("ViewModelHeadBob: No HeadBob component found in the scene. Please assign one manually.");
                    enabled = false;
                    return;
                }
            }
        }

        initialLocalPosition = transform.localPosition;
        initialLocalRotation = transform.localRotation;

        if (parentHeadBob.playerCamera != null)
        {
            lastCameraRotationQuat = parentHeadBob.playerCamera.transform.rotation;
            lastCameraEulerAngles = parentHeadBob.playerCamera.transform.eulerAngles;
            lastCameraYaw = lastCameraEulerAngles.y;
        }
        
        RegenerateRandomBounceDirection();
        
        if (collisionCheckPoints == null || collisionCheckPoints.Length == 0)
        {
            collisionCheckPoints = new Vector3[]
            {
                new Vector3(0, 0, 0.2f),
                new Vector3(0.05f, 0, 0.15f),
                new Vector3(-0.05f, 0, 0.15f),
                new Vector3(0, 0.05f, 0.15f)
            };
        }
        
        if (wallLayers.value == 0)
        {
            wallLayers = Physics.DefaultRaycastLayers & ~(1 << LayerMask.NameToLayer("Player"));
        }
        
        cachedTransform = transform;
        if (parentHeadBob?.playerCamera != null)
        {
            cachedCameraTransform = parentHeadBob.playerCamera.transform;
        }
        
        raycastHits = new RaycastHit[MAX_RAYCAST_HITS];
        nextWallCheckTime = 0f;
    }

    private void Update()
    {
        // Check grappling state
        if (parentHeadBob?.grapplingHookSystem != null)
        {
            isGrappling = parentHeadBob.grapplingHookSystem.IsSwinging;
        }
        
        // Update grappling transition blend
        float targetBlend = isGrappling ? 1f : 0f;
        grapplingTransitionBlend = Mathf.Lerp(grapplingTransitionBlend, targetBlend, Time.deltaTime * grapplingStabilizationSpeed);
        
        isFlying = DebugFlyController.IsFlying;
        
        if (isFlying)
        {
            effectiveShakeMultiplier = Mathf.Lerp(effectiveShakeMultiplier, 0f, Time.deltaTime * terminalShakeDamping);
        }
        else
        {
            effectiveShakeMultiplier = Mathf.Lerp(effectiveShakeMultiplier, maxTerminalShakeMultiplier, Time.deltaTime * 2f);
        }
    }

    private void LateUpdate()
    {
        if (parentHeadBob == null)
            return;

        ApplyHeadBobEffects();

        if (parentHeadBob.playerCamera != null)
        {
            // Only apply camera motion if not grappling (or reduced if grappling)
            if (!disableCameraMotionWhileGrappling || !isGrappling)
            {
                ApplyCameraMotionOffset();
            }
            
            if (enableWallCollisionPrevention && !isFlying)
            {
                CheckWallCollision();
            }
            else
            {
                wallAvoidanceOffset = Vector3.Lerp(wallAvoidanceOffset, Vector3.zero, Time.deltaTime * wallAvoidanceSpeed);
                wallAvoidanceRotation = Quaternion.Slerp(wallAvoidanceRotation, Quaternion.identity, Time.deltaTime * wallAvoidanceSpeed);
                isCollidingWithWall = false;
            }
        }
    }

    private void ApplyHeadBobEffects()
    {
        Vector3 bobOffset = Vector3.zero;
        Quaternion bobRotation = Quaternion.identity;
        Vector3 momentumOffset = Vector3.zero;
        Vector3 impactOffset = Vector3.zero;
        Vector3 shakeOffset = Vector3.zero;

        bool isSliding = false;
        if (parentHeadBob.fpsController != null)
        {
            isSliding = parentHeadBob.fpsController.CurrentCharacterState == CharacterState.Sliding;
        }

        // Calculate the reduction factor based on grappling state
        float effectReduction = Mathf.Lerp(1f, grapplingEffectReduction, grapplingTransitionBlend);

        if (!isFlying)
        {
            // Apply effects with grappling reduction
            bobOffset = parentHeadBob.DebugCurrentBobOffset * bobPositionMultiplier * effectReduction;
            bobRotation = Quaternion.Slerp(
                Quaternion.identity,
                parentHeadBob.DebugCurrentBobRotation,
                bobRotationMultiplier * effectReduction
            );
            momentumOffset = parentHeadBob.DebugMomentumOffset * bobPositionMultiplier * effectReduction;

            // Landing bounce is not affected by grappling reduction
            if (parentHeadBob.DebugIsLandingBounce)
            {
                float bounceAmount = parentHeadBob.DebugLandingBounceForce * bounceMultiplier;
                
                if (parentHeadBob.DebugLandingBounceTimer < 0.05f)
                {
                    RegenerateRandomBounceDirection();
                }
                
                float duration = parentHeadBob.DebugLandingBounceDuration;
                float t = Mathf.Clamp01(parentHeadBob.DebugLandingBounceTimer / duration);
                
                if (!parentHeadBob.DebugLandingBounceErratic)
                {
                    float damping = 5f;
                    float bounce = bounceAmount * Mathf.Sin(t * Mathf.PI) * Mathf.Exp(-t * damping);
                    impactOffset = Vector3.down * bounce * landingVerticalMultiplier;
                }
                else
                {
                    float shakeDecay = 1f - t;
                    float shakeIntensity = bounceAmount * shakeDecay;
                    
                    float noiseTime = Time.time * parentHeadBob.DebugLandingBounceFrequency * 5f;
                    Vector3 erraticMotion = new Vector3(
                        (Mathf.PerlinNoise(noiseTime, 0f) * 2f - 1f) * erraticHorizontalMultiplier,
                        (Mathf.PerlinNoise(0f, noiseTime) * 2f - 1f),
                        (Mathf.PerlinNoise(noiseTime, noiseTime) * 2f - 1f) * erraticHorizontalMultiplier * 0.5f
                    );
                    
                    Vector3 stableMotion = randomBounceDirection * (1f - t * 2f);
                    Vector3 combinedMotion = Vector3.Lerp(stableMotion, erraticMotion, t * 0.7f);
                    combinedMotion.y *= landingVerticalMultiplier;
                    
                    impactOffset = combinedMotion * shakeIntensity;
                }
            }
        }

        // Terminal velocity shake (reduced when grappling)
        if (parentHeadBob.DebugTerminalVelocityShakeIntensity > 0 && !isGrappling)
        {
            float shakeIntensity = parentHeadBob.DebugTerminalVelocityShakeIntensity * effectiveShakeMultiplier * effectReduction;
            
            if (shakeIntensity > 0.001f)
            {
                float noiseTime = Time.time * 40f;
                shakeOffset = new Vector3(
                    (Mathf.PerlinNoise(noiseTime, 0f) * 2f - 1f) * 0.5f,
                    (Mathf.PerlinNoise(0f, noiseTime) * 2f - 1f) * 0.5f,
                    0f
                ) * shakeIntensity;
            }
        }

        // Slide effects (reduced when grappling)
        Vector3 slideTiltOffset = parentHeadBob.DebugSlideTiltRotation * bobRotationMultiplier * effectReduction;
        Quaternion slideTilt = Quaternion.Euler(slideTiltOffset);
        
        if (isSliding)
        {
            Quaternion targetSlideViewModelRotation = Quaternion.Euler(slideViewModelRotation);
            currentSlideViewModelRotation = Quaternion.Slerp(
                currentSlideViewModelRotation, 
                targetSlideViewModelRotation, 
                Time.deltaTime * slideViewModelSpeed
            );
            
            currentSlideViewModelPosition = Vector3.Lerp(
                currentSlideViewModelPosition,
                slideViewModelOffset,
                Time.deltaTime * slideViewModelSpeed
            );
            
            wasSliding = true;
        }
        else if (wasSliding)
        {
            currentSlideViewModelRotation = Quaternion.Slerp(
                currentSlideViewModelRotation, 
                Quaternion.identity, 
                Time.deltaTime * slideViewModelSpeed
            );
            
            currentSlideViewModelPosition = Vector3.Lerp(
                currentSlideViewModelPosition,
                Vector3.zero,
                Time.deltaTime * slideViewModelSpeed
            );
            
            if (Quaternion.Angle(currentSlideViewModelRotation, Quaternion.identity) < 0.1f 
                && currentSlideViewModelPosition.magnitude < 0.01f)
            {
                currentSlideViewModelRotation = Quaternion.identity;
                currentSlideViewModelPosition = Vector3.zero;
                wasSliding = false;
            }
        }
        
        // Side sway (disabled or reduced when grappling)
        Vector3 sideSwayOffset = Vector3.zero;
        if (enableSideSway && (!disableSideSwayWhileGrappling || !isGrappling))
        {
            sideSwayOffset = Vector3.right * currentSideSwayOffset * effectReduction;
        }
        
        // Apply all offsets
        Vector3 finalPosition = initialLocalPosition + bobOffset + momentumOffset + impactOffset + shakeOffset + sideSwayOffset + wallAvoidanceOffset + currentSlideViewModelPosition;
        Quaternion finalRotation = initialLocalRotation * bobRotation * slideTilt * currentSlideViewModelRotation * wallAvoidanceRotation;
        
        transform.localPosition = finalPosition;
        transform.localRotation = finalRotation;
    }
    
    private void RegenerateRandomBounceDirection()
    {
        randomBounceDirection = new Vector3(
            Random.Range(-1f, 1f) * erraticHorizontalMultiplier,
            Random.Range(-1f, 0f),
            Random.Range(-1f, 1f) * erraticHorizontalMultiplier * 0.5f
        ).normalized;
    }

    private void ApplyCameraMotionOffset()
    {
        // Skip camera motion when sliding or grappling (if disabled)
        if (parentHeadBob.fpsController != null && parentHeadBob.fpsController.CurrentCharacterState == CharacterState.Sliding)
            return;
            
        if (disableCameraMotionWhileGrappling && isGrappling)
            return;
            
        Transform cameraTransform = parentHeadBob.playerCamera.transform;

        Vector3 currentRotation = cameraTransform.eulerAngles;
        Vector3 rotationDelta = NormalizeEulerAngleDelta(currentRotation - lastCameraEulerAngles);

        currentCameraVelocity = rotationDelta / Time.deltaTime;
        
        // Apply stronger smoothing when grappling to reduce jitter
        float actualSmoothingFactor = isGrappling ? 0.95f : (isFlying ? 0.95f : cameraSmoothingFactor);
        smoothedCameraVelocity = Vector3.Lerp(smoothedCameraVelocity, currentCameraVelocity, 1f - actualSmoothingFactor);

        smoothedCameraVelocity = Vector3.ClampMagnitude(smoothedCameraVelocity, 300f);

        Vector3 normalizedVelocity = Vector3.zero;
        if (smoothedCameraVelocity.magnitude > cameraDeadzone)
        {
            float velocityScaleFactor = isGrappling ? 480f : (isFlying ? 240f : 120f);
            normalizedVelocity = smoothedCameraVelocity / velocityScaleFactor;
        }

        // Reduce effect when grappling
        float grapplingMultiplier = Mathf.Lerp(1f, 0.2f, grapplingTransitionBlend);
        
        Vector3 positionOffset = new Vector3(
            -normalizedVelocity.y * maxPositionOffset.x,
            normalizedVelocity.x * maxPositionOffset.y,
            0f
        ) * grapplingMultiplier;

        Vector3 rotationOffset = new Vector3(
            -normalizedVelocity.y * maxRotationOffset.x,
            normalizedVelocity.x * maxRotationOffset.y,
            -normalizedVelocity.x * maxRotationOffset.z
        ) * grapplingMultiplier;

        float effectiveFollowSpeed = isGrappling ? cameraFollowSpeed * 0.3f : (isFlying ? cameraFollowSpeed * 0.5f : cameraFollowSpeed);

        Vector3 targetPosition = initialLocalPosition + positionOffset;
        transform.localPosition = Vector3.Lerp(transform.localPosition, targetPosition, Time.deltaTime * effectiveFollowSpeed);

        Quaternion targetRotation = initialLocalRotation * Quaternion.Euler(rotationOffset);
        transform.localRotation = Quaternion.Slerp(transform.localRotation, targetRotation, Time.deltaTime * effectiveFollowSpeed);

        // Side sway
        if (enableSideSway && !isFlying && (!disableSideSwayWhileGrappling || !isGrappling))
        {
            Quaternion currentCameraRotation = cameraTransform.rotation;
            float currentYaw = currentRotation.y;
            float yawDelta = NormalizeAngle(currentYaw - lastCameraYaw);
            
            float swayImpulse = -yawDelta * sideSwayAmount * 0.05f * grapplingMultiplier;
            currentSideSwayOffset += swayImpulse;
            currentSideSwayOffset = Mathf.Clamp(currentSideSwayOffset, -maxSideSwayDistance, maxSideSwayDistance);
            
            float returnStrength = 8f;
            float returnForce = -currentSideSwayOffset * returnStrength * Time.deltaTime;
            currentSideSwayOffset += returnForce;
            
            lastCameraYaw = currentYaw;
            lastCameraRotationQuat = currentCameraRotation;
        }
        else
        {
            float returnStrength = 15f;
            currentSideSwayOffset += -currentSideSwayOffset * returnStrength * Time.deltaTime;
            
            if (Mathf.Abs(currentSideSwayOffset) < 0.001f)
            {
                currentSideSwayOffset = 0f;
            }
            
            lastCameraYaw = currentRotation.y;
            lastCameraRotationQuat = cameraTransform.rotation;
        }

        lastCameraEulerAngles = currentRotation;
    }
    
    private void CheckWallCollision()
    {
        // Reduce wall collision checks when grappling for performance
        float adjustedCheckInterval = isGrappling ? wallCheckInterval * 2f : wallCheckInterval;
        
        if (Time.time < nextWallCheckTime)
        {
            return;
        }
        nextWallCheckTime = Time.time + adjustedCheckInterval;

        bool hitWall = false;
        float closestDistance = wallCheckDistance;
        Vector3 closestHitPoint = Vector3.zero;
        Vector3 closestHitNormal = Vector3.forward;
        
        Vector3 cameraPosition = cachedCameraTransform.position;
        Vector3 cameraForward = cachedCameraTransform.forward;
        
        foreach (Vector3 localCheckPoint in collisionCheckPoints)
        {
            Vector3 worldCheckPoint = cachedTransform.TransformPoint(localCheckPoint);
            
            if (showDebugVisuals)
            {
                Debug.DrawLine(cameraPosition, worldCheckPoint, Color.yellow, adjustedCheckInterval);
            }
            
            Vector3 checkDirection = worldCheckPoint - cameraPosition;
            float checkDistance = checkDirection.magnitude;
            
            int hitCount = Physics.RaycastNonAlloc(cameraPosition, checkDirection.normalized, 
                raycastHits, checkDistance + wallCheckDistance, wallLayers);
            
            for (int i = 0; i < hitCount; i++)
            {
                var hit = raycastHits[i];
                
                if (showDebugVisuals)
                {
                    Debug.DrawLine(hit.point, hit.point + hit.normal * 0.05f, Color.red, adjustedCheckInterval);
                }
                
                if (hit.distance < checkDistance)
                {
                    hitWall = true;
                    
                    float distanceToModel = checkDistance - hit.distance;
                    if (distanceToModel < closestDistance)
                    {
                        closestDistance = distanceToModel;
                        closestHitPoint = hit.point;
                        closestHitNormal = hit.normal;
                    }
                }
            }
            
            if (showDebugVisuals)
            {
                Debug.DrawRay(worldCheckPoint, cameraForward * wallCheckDistance, Color.blue, adjustedCheckInterval);
            }
            
            hitCount = Physics.RaycastNonAlloc(worldCheckPoint, cameraForward, 
                raycastHits, wallCheckDistance, wallLayers);
            
            for (int i = 0; i < hitCount; i++)
            {
                var forwardHit = raycastHits[i];
                hitWall = true;
                
                if (forwardHit.distance < closestDistance)
                {
                    closestDistance = forwardHit.distance;
                    closestHitPoint = forwardHit.point;
                    closestHitNormal = forwardHit.normal;
                }
            }
        }
        
        if (hitWall)
        {
            float adjustedWallAvoidanceSpeed = isCollidingWithWall ? wallAvoidanceSpeed * 1.5f : wallAvoidanceSpeed;
            float pushStrength = 1.0f - (closestDistance / wallCheckDistance);
            
            Vector3 localNormal = cachedCameraTransform.InverseTransformDirection(closestHitNormal);
            Vector3 targetLocalOffset = localNormal * pushStrength * maxWallPushDistance;
            targetLocalOffset.z -= pushStrength * 0.03f;
            targetLocalOffset.y -= pushStrength * 0.02f;
            
            Vector3 targetWorldOffset = cachedCameraTransform.TransformDirection(targetLocalOffset);
            
            if (showDebugVisuals)
            {
                Debug.DrawRay(transform.position, targetWorldOffset * 10f, Color.green, adjustedCheckInterval);
            }
            
            Vector3 targetPushOffset = cachedTransform.InverseTransformDirection(targetWorldOffset);
            
            wallAvoidanceOffset = Vector3.Lerp(wallAvoidanceOffset, targetPushOffset, 
                                             Time.deltaTime * adjustedWallAvoidanceSpeed);
            
            Vector3 rotAdjust = new Vector3(
                localNormal.y * wallCollisionRotation.x,
                localNormal.x * wallCollisionRotation.y,
                localNormal.z * wallCollisionRotation.z
            );
            
            wallAvoidanceRotation = Quaternion.Slerp(wallAvoidanceRotation, 
                                                  Quaternion.Euler(rotAdjust * pushStrength), 
                                                  Time.deltaTime * adjustedWallAvoidanceSpeed);
            
            isCollidingWithWall = true;
        }
        else if (isCollidingWithWall)
        {
            wallAvoidanceOffset = Vector3.Lerp(wallAvoidanceOffset, Vector3.zero, 
                                             Time.deltaTime * wallAvoidanceSpeed * 0.5f);
            
            wallAvoidanceRotation = Quaternion.Slerp(wallAvoidanceRotation, Quaternion.identity, 
                                                  Time.deltaTime * wallAvoidanceSpeed * 0.5f);
            
            if (wallAvoidanceOffset.magnitude < 0.001f)
            {
                wallAvoidanceOffset = Vector3.zero;
                wallAvoidanceRotation = Quaternion.identity;
                isCollidingWithWall = false;
            }
        }
    }

    private Vector3 NormalizeEulerAngleDelta(Vector3 eulerDelta)
    {
        if (eulerDelta.x > 180f) eulerDelta.x -= 360f;
        if (eulerDelta.x < -180f) eulerDelta.x += 360f;

        if (eulerDelta.y > 180f) eulerDelta.y -= 360f;
        if (eulerDelta.y < -180f) eulerDelta.y += 360f;

        if (eulerDelta.z > 180f) eulerDelta.z -= 360f;
        if (eulerDelta.z < -180f) eulerDelta.z += 360f;

        return eulerDelta;
    }
    
    private float NormalizeAngle(float angle)
    {
        if (angle > 180f) angle -= 360f;
        if (angle < -180f) angle += 360f;
        return angle;
    }
}