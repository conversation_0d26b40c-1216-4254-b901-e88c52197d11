using UnityEngine;
using KinematicCharacterController.FPS;

/// <summary>
/// Zoom helmet boon effect that provides optical zoom functionality when ZoomHelmet boon is equipped.
/// Integrates with FOVManager to modify camera field of view and applies sensitivity reduction during zoom.
/// </summary>
public class ZoomEffect : BoonEffectBase
{
    [Header("Zoom Settings")]
    [SerializeField] private KeyCode zoomKey = KeyCode.X;
    [SerializeField] private float zoomedFOV = 30f;
    [SerializeField] private float zoomSensitivityModifier = 0.5f;

    [Header("Zoom Transition Settings")]
    [SerializeField] private float zoomInSpeed = 8f;
    [SerializeField] private float zoomOutSpeed = 6f;
    [SerializeField] private AnimationCurve zoomInCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
    [SerializeField] private AnimationCurve zoomOutCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
    [SerializeField] private bool smoothSensitivityTransition = true;
    [SerializeField] private float sensitivityTransitionSpeed = 10f;

    private bool isZooming = false;
    private bool isTransitioning = false;
    private float normalFOV;
    private float currentFOVProgress = 0f; // 0 = normal, 1 = fully zoomed
    private float targetFOVProgress = 0f;
    private float currentSensitivityModifier = 1f;
    private float targetSensitivityModifier = 1f;
    private FPSCharacterCamera cameraController;
    
    public override BoonType BoonType => BoonType.ZoomHelmet;
    
    public override void Initialize(PlayerStatus status, EquipmentManager equipment, FPSCharacterController controller, Camera cameraRef, HeadBob headBobRef)
    {
        base.Initialize(status, equipment, controller, cameraRef, headBobRef);
        
        // Get camera controller reference
        if (cameraRef != null)
        {
            cameraController = cameraRef.GetComponent<FPSCharacterCamera>();
        }
        
        // Store normal FOV
        if (cameraRef != null)
        {
            normalFOV = cameraRef.fieldOfView;
        }
    }
    
    public override void OnBoonEnabled()
    {
        base.OnBoonEnabled();
        
        // Reset zoom state when boon is enabled
        isZooming = false;
        
        // Update normal FOV in case it changed
        if (playerCamera != null)
        {
            normalFOV = playerCamera.fieldOfView;
        }
    }
    
    public override void OnBoonDisabled()
    {
        // Stop zoom if currently zooming and force immediate transition
        if (isZooming || isTransitioning)
        {
            isZooming = false;
            isTransitioning = false;
            currentFOVProgress = 0f;
            targetFOVProgress = 0f;
            currentSensitivityModifier = 1f;
            targetSensitivityModifier = 1f;

            // Remove zoom modifier from FOVManager
            if (FOVManager.Instance != null)
            {
                FOVManager.Instance.RemoveFOVModifier("HelmetZoom");
            }

            // Reset sensitivity modifier
            if (cameraController != null)
            {
                cameraController.SetZoomModifier(1.0f);
            }
        }

        base.OnBoonDisabled();
    }
    
    private void Update()
    {
        if (!enabled || playerCamera == null) return;

        // Don't handle zoom if camera controller is in restricted modes
        if (cameraController != null && !CanZoom())
        {
            if (isZooming)
            {
                StopZoom();
            }
            return;
        }

        HandleZoomInput();
        UpdateZoomTransitions();
    }
    
    private bool CanZoom()
    {
        // Check if camera controller allows zooming (not in inventory, grab mode, etc.)
        if (cameraController == null) return true;
        
        // We can zoom if we're not in restricted modes
        // Note: We don't check _canZoom here since that's tool-specific, 
        // but helmet zoom should work regardless of selected tool
        return true;
    }
    
    private void HandleZoomInput()
    {
        bool zoomKeyPressed = Input.GetKey(zoomKey);
        
        if (zoomKeyPressed && !isZooming)
        {
            StartZoom();
        }
        else if (!zoomKeyPressed && isZooming)
        {
            StopZoom();
        }
    }
    
    private void StartZoom()
    {
        if (isZooming) return;

        isZooming = true;
        isTransitioning = true;
        targetFOVProgress = 1f;
        targetSensitivityModifier = zoomSensitivityModifier;

        // Ensure cursor remains locked during zoom
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    private void StopZoom()
    {
        if (!isZooming) return;

        isZooming = false;
        isTransitioning = true;
        targetFOVProgress = 0f;
        targetSensitivityModifier = 1f;
    }

    private void UpdateZoomTransitions()
    {
        if (!isTransitioning) return;

        bool fovNeedsUpdate = false;
        bool sensitivityNeedsUpdate = false;

        // Update FOV progress
        if (!Mathf.Approximately(currentFOVProgress, targetFOVProgress))
        {
            float speed = targetFOVProgress > currentFOVProgress ? zoomInSpeed : zoomOutSpeed;
            AnimationCurve curve = targetFOVProgress > currentFOVProgress ? zoomInCurve : zoomOutCurve;

            currentFOVProgress = Mathf.MoveTowards(currentFOVProgress, targetFOVProgress, speed * Time.deltaTime);

            // Apply easing curve
            float easedProgress = curve.Evaluate(currentFOVProgress);
            float currentFOV = Mathf.Lerp(normalFOV, zoomedFOV, easedProgress);
            float zoomModifier = currentFOV - normalFOV;

            if (FOVManager.Instance != null)
            {
                FOVManager.Instance.SetFOVModifier("HelmetZoom", zoomModifier);
            }

            fovNeedsUpdate = true;
        }

        // Update sensitivity modifier
        if (smoothSensitivityTransition && !Mathf.Approximately(currentSensitivityModifier, targetSensitivityModifier))
        {
            currentSensitivityModifier = Mathf.MoveTowards(currentSensitivityModifier, targetSensitivityModifier, sensitivityTransitionSpeed * Time.deltaTime);

            if (cameraController != null)
            {
                cameraController.SetZoomModifier(currentSensitivityModifier);
            }

            sensitivityNeedsUpdate = true;
        }
        else if (!smoothSensitivityTransition && !Mathf.Approximately(currentSensitivityModifier, targetSensitivityModifier))
        {
            // Instant sensitivity change
            currentSensitivityModifier = targetSensitivityModifier;

            if (cameraController != null)
            {
                cameraController.SetZoomModifier(currentSensitivityModifier);
            }

            sensitivityNeedsUpdate = true;
        }

        // Check if transition is complete
        if (!fovNeedsUpdate && !sensitivityNeedsUpdate)
        {
            isTransitioning = false;

            // Clean up FOV modifier if we're back to normal
            if (Mathf.Approximately(targetFOVProgress, 0f) && FOVManager.Instance != null)
            {
                FOVManager.Instance.RemoveFOVModifier("HelmetZoom");
            }
        }
    }

    /// <summary>
    /// Public property to check if currently zooming (for other systems)
    /// </summary>
    public bool IsCurrentlyZooming => isZooming;
}