{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"BakeryEditorAssembly": "1.0.0", "BakeryRuntimeAssembly": "1.0.0", "Tayx.Graphy": "1.0.0", "Tayx.Graphy.Editor": "1.0.0", "Unity.RenderPipelines.Core.Samples.Editor": "1.0.0", "Unity.RenderPipelines.Core.Samples.Runtime": "1.0.0", "Unity.RenderPipelines.HighDefinition.Samples.Common.Editor": "1.0.0", "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp.dll": {}}}, "BakeryEditorAssembly/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"BakeryRuntimeAssembly": "1.0.0"}, "compile": {"bin/placeholder/BakeryEditorAssembly.dll": {}}, "runtime": {"bin/placeholder/BakeryEditorAssembly.dll": {}}}, "BakeryRuntimeAssembly/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/BakeryRuntimeAssembly.dll": {}}, "runtime": {"bin/placeholder/BakeryRuntimeAssembly.dll": {}}}, "Tayx.Graphy/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Tayx.Graphy.dll": {}}, "runtime": {"bin/placeholder/Tayx.Graphy.dll": {}}}, "Tayx.Graphy.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Tayx.Graphy": "1.0.0"}, "compile": {"bin/placeholder/Tayx.Graphy.Editor.dll": {}}, "runtime": {"bin/placeholder/Tayx.Graphy.Editor.dll": {}}}, "Unity.RenderPipelines.Core.Samples.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Samples.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Samples.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Samples.Editor.dll": {}}}, "Unity.RenderPipelines.Core.Samples.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Samples.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Samples.Runtime.dll": {}}}, "Unity.RenderPipelines.HighDefinition.Samples.Common.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Samples.Runtime": "1.0.0", "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.HighDefinition.Samples.Common.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.HighDefinition.Samples.Common.Editor.dll": {}}}, "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Samples.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll": {}}}}}, "libraries": {"Assembly-CSharp/1.0.0": {"type": "project", "path": "Assembly-CSharp.csproj", "msbuildProject": "Assembly-CSharp.csproj"}, "BakeryEditorAssembly/1.0.0": {"type": "project", "path": "BakeryEditorAssembly.csproj", "msbuildProject": "BakeryEditorAssembly.csproj"}, "BakeryRuntimeAssembly/1.0.0": {"type": "project", "path": "BakeryRuntimeAssembly.csproj", "msbuildProject": "BakeryRuntimeAssembly.csproj"}, "Tayx.Graphy/1.0.0": {"type": "project", "path": "Tayx.Graphy.csproj", "msbuildProject": "Tayx.Graphy.csproj"}, "Tayx.Graphy.Editor/1.0.0": {"type": "project", "path": "Tayx.Graphy.Editor.csproj", "msbuildProject": "Tayx.Graphy.Editor.csproj"}, "Unity.RenderPipelines.Core.Samples.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Samples.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Samples.Editor.csproj"}, "Unity.RenderPipelines.Core.Samples.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Samples.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Samples.Runtime.csproj"}, "Unity.RenderPipelines.HighDefinition.Samples.Common.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.HighDefinition.Samples.Common.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.HighDefinition.Samples.Common.Editor.csproj"}, "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp >= 1.0.0", "BakeryEditorAssembly >= 1.0.0", "BakeryRuntimeAssembly >= 1.0.0", "Tayx.Graphy >= 1.0.0", "Tayx.Graphy.Editor >= 1.0.0", "Unity.RenderPipelines.Core.Samples.Editor >= 1.0.0", "Unity.RenderPipelines.Core.Samples.Runtime >= 1.0.0", "Unity.RenderPipelines.HighDefinition.Samples.Common.Editor >= 1.0.0", "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Unity\\BLAME\\BLAME\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "C:\\Unity\\BLAME\\BLAME\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Unity\\BLAME\\BLAME\\Temp\\obj\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Unity\\BLAME\\BLAME\\Assembly-CSharp.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\Assembly-CSharp.csproj"}, "C:\\Unity\\BLAME\\BLAME\\BakeryEditorAssembly.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\BakeryEditorAssembly.csproj"}, "C:\\Unity\\BLAME\\BLAME\\BakeryRuntimeAssembly.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\BakeryRuntimeAssembly.csproj"}, "C:\\Unity\\BLAME\\BLAME\\Tayx.Graphy.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\Tayx.Graphy.csproj"}, "C:\\Unity\\BLAME\\BLAME\\Tayx.Graphy.Editor.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\Tayx.Graphy.Editor.csproj"}, "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.Core.Samples.Editor.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.Core.Samples.Editor.csproj"}, "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.Core.Samples.Runtime.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.Core.Samples.Runtime.csproj"}, "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.HighDefinition.Samples.Common.Editor.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.HighDefinition.Samples.Common.Editor.csproj"}, "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.202\\RuntimeIdentifierGraph.json"}}}}