{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Unity.RenderPipelines.Core.Samples.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Samples.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Samples.Runtime.dll": {}}}, "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Samples.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll": {}}}}}, "libraries": {"Unity.RenderPipelines.Core.Samples.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Samples.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Samples.Runtime.csproj"}, "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Unity.RenderPipelines.Core.Samples.Runtime >= 1.0.0", "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.HighDefinition.Samples.Common.Editor.csproj", "projectName": "Unity.RenderPipelines.HighDefinition.Samples.Common.Editor", "projectPath": "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.HighDefinition.Samples.Common.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Unity\\BLAME\\BLAME\\Temp\\obj\\Unity.RenderPipelines.HighDefinition.Samples.Common.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.Core.Samples.Runtime.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.Core.Samples.Runtime.csproj"}, "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.202\\RuntimeIdentifierGraph.json"}}}}