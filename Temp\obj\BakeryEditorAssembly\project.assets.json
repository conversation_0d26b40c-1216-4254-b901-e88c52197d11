{"version": 3, "targets": {".NETStandard,Version=v2.1": {"BakeryRuntimeAssembly/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/BakeryRuntimeAssembly.dll": {}}, "runtime": {"bin/placeholder/BakeryRuntimeAssembly.dll": {}}}}}, "libraries": {"BakeryRuntimeAssembly/1.0.0": {"type": "project", "path": "BakeryRuntimeAssembly.csproj", "msbuildProject": "BakeryRuntimeAssembly.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["BakeryRuntimeAssembly >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Unity\\BLAME\\BLAME\\BakeryEditorAssembly.csproj", "projectName": "BakeryEditorAssembly", "projectPath": "C:\\Unity\\BLAME\\BLAME\\BakeryEditorAssembly.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Unity\\BLAME\\BLAME\\Temp\\obj\\BakeryEditorAssembly\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Unity\\BLAME\\BLAME\\BakeryRuntimeAssembly.csproj": {"projectPath": "C:\\Unity\\BLAME\\BLAME\\BakeryRuntimeAssembly.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.202\\RuntimeIdentifierGraph.json"}}}}