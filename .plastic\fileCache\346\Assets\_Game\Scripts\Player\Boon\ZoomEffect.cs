using UnityEngine;
using KinematicCharacterController.FPS;

/// <summary>
/// Zoom helmet boon effect that provides optical zoom functionality when ZoomHelmet boon is equipped.
/// Integrates with FOVManager to modify camera field of view and applies sensitivity reduction during zoom.
/// </summary>
public class ZoomEffect : BoonEffectBase
{
    [Header("Zoom Settings")]
    [SerializeField] private KeyCode zoomKey = KeyCode.X;
    [SerializeField] private float zoomedFOV = 30f;
    [SerializeField] private float zoomSensitivityModifier = 0.5f;
    
    private bool isZooming = false;
    private float normalFOV;
    private FPSCharacterCamera cameraController;
    
    public override BoonType BoonType => BoonType.ZoomHelmet;
    
    public override void Initialize(PlayerStatus status, EquipmentManager equipment, FPSCharacterController controller, Camera cameraRef, HeadBob headBobRef)
    {
        base.Initialize(status, equipment, controller, cameraRef, headBobRef);
        
        // Get camera controller reference
        if (cameraRef != null)
        {
            cameraController = cameraRef.GetComponent<FPSCharacterCamera>();
        }
        
        // Store normal FOV
        if (cameraRef != null)
        {
            normalFOV = cameraRef.fieldOfView;
        }
    }
    
    public override void OnBoonEnabled()
    {
        base.OnBoonEnabled();
        
        // Reset zoom state when boon is enabled
        isZooming = false;
        
        // Update normal FOV in case it changed
        if (playerCamera != null)
        {
            normalFOV = playerCamera.fieldOfView;
        }
    }
    
    public override void OnBoonDisabled()
    {
        // Stop zoom if currently zooming
        if (isZooming)
        {
            StopZoom();
        }
        
        base.OnBoonDisabled();
    }
    
    private void Update()
    {
        if (!enabled || playerCamera == null) return;
        
        // Don't handle zoom if camera controller is in restricted modes
        if (cameraController != null && !CanZoom())
        {
            if (isZooming)
            {
                StopZoom();
            }
            return;
        }
        
        HandleZoomInput();
    }
    
    private bool CanZoom()
    {
        // Check if camera controller allows zooming (not in inventory, grab mode, etc.)
        if (cameraController == null) return true;
        
        // We can zoom if we're not in restricted modes
        // Note: We don't check _canZoom here since that's tool-specific, 
        // but helmet zoom should work regardless of selected tool
        return true;
    }
    
    private void HandleZoomInput()
    {
        bool zoomKeyPressed = Input.GetKey(zoomKey);
        
        if (zoomKeyPressed && !isZooming)
        {
            StartZoom();
        }
        else if (!zoomKeyPressed && isZooming)
        {
            StopZoom();
        }
    }
    
    private void StartZoom()
    {
        if (isZooming) return;
        
        isZooming = true;
        
        // Apply zoom through FOVManager
        float zoomModifier = zoomedFOV - normalFOV;
        FOVManager.Instance?.SetFOVModifier("HelmetZoom", zoomModifier);
        
        // Apply sensitivity modifier through camera controller if available
        if (cameraController != null)
        {
            cameraController.SetZoomModifier(zoomSensitivityModifier);
        }
        
        // Ensure cursor remains locked during zoom
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }
    
    private void StopZoom()
    {
        if (!isZooming) return;
        
        isZooming = false;
        
        // Remove zoom modifier from FOVManager
        FOVManager.Instance?.RemoveFOVModifier("HelmetZoom");
        
        // Reset sensitivity modifier through camera controller if available
        if (cameraController != null)
        {
            cameraController.SetZoomModifier(1.0f);
        }
    }
    
    /// <summary>
    /// Public property to check if currently zooming (for other systems)
    /// </summary>
    public bool IsCurrentlyZooming => isZooming;
}