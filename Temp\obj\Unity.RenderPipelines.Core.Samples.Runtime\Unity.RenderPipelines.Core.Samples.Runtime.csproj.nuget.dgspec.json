{"format": 1, "restore": {"C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.Core.Samples.Runtime.csproj": {}}, "projects": {"C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.Core.Samples.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.Core.Samples.Runtime.csproj", "projectName": "Unity.RenderPipelines.Core.Samples.Runtime", "projectPath": "C:\\Unity\\BLAME\\BLAME\\Unity.RenderPipelines.Core.Samples.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Unity\\BLAME\\BLAME\\Temp\\obj\\Unity.RenderPipelines.Core.Samples.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.202\\RuntimeIdentifierGraph.json"}}}}}